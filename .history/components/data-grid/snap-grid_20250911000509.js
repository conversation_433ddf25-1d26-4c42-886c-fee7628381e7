/**
 * SnapGrid - Advanced Data Grid Engine
 * A comprehensive client-side data grid inspired by AG Grid
 * Following the single-file architecture pattern of snap-charts.js
 *
 * Features:
 * - Virtual scrolling for performance with large datasets
 * - Column menus with sorting, filtering, grouping
 * - Client-side data operations
 * - Cell editing and custom renderers
 * - Performance monitoring integration
 * - Modern UI design matching Snap Dashboard
 *
 * @version 1.0.0
 * <AUTHOR> Dashboard Team
 */

class SnapGrid {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.getElementById(container) : container;
        if (!this.container) {
            throw new Error('SnapGrid: Container element not found');
        }

        // Core configuration
        this.options = {
            // Data options
            data: options.data || [],
            columns: options.columns || [],

            // Performance options
            virtualScrolling: options.virtualScrolling !== false,
            rowHeight: options.rowHeight || 40,
            headerHeight: options.headerHeight || 48,
            bufferSize: options.bufferSize || 10,

            // Feature options
            sortable: options.sortable !== false,
            filterable: options.filterable !== false,
            resizable: options.resizable !== false,
            selectable: options.selectable !== false,
            editable: options.editable || false,

            // Checkbox and selection options
            checkboxSelection: options.checkboxSelection !== false,
            headerCheckboxSelection: options.headerCheckboxSelection !== false,

            // Column options
            columnDragging: options.columnDragging !== false,
            pinnedColumns: options.pinnedColumns || ['checkbox'], // Default pinned columns

            // UI options
            theme: options.theme || 'default', // default, compact, dense, comfortable
            showHeader: options.showHeader !== false,
            showFooter: options.showFooter || false,

            // Callbacks
            onRowClick: options.onRowClick || null,
            onCellEdit: options.onCellEdit || null,
            onSort: options.onSort || null,
            onFilter: options.onFilter || null,
            onSelectionChanged: options.onSelectionChanged || null,
            onColumnMoved: options.onColumnMoved || null,

            ...options,
            // Enforce non-editable grid and footer visible by default
            editable: false,
            showFooter: options.showFooter !== false
        };

        // Internal state
        this.data = [...this.options.data];
        this.filteredData = [...this.data];
        this.sortedData = [...this.filteredData];
        this.selectedRows = new Set();
        this.sortState = {};
        this.filterState = {};
        this.columnState = {};

        // Selection state
        this.allRowsSelected = false;
        this.indeterminateSelection = false;

        // Column dragging state
        this.isDraggingColumn = false;
        this.draggedColumn = null;
        this.dragStartX = 0;
        this.dragStartIndex = 0;

        // Prepare columns with checkbox column if enabled
        this.prepareColumns();
        
        // Virtual scrolling state
        this.scrollTop = 0;
        this.visibleStartIndex = 0;
        this.visibleEndIndex = 0;
        this.totalHeight = 0;
        
        // DOM elements
        this.gridElement = null;
        this.headerElement = null;
        this.bodyElement = null;
        this.viewportElement = null;
        this.scrollbarElement = null;

        // Pinned column elements (old grid architecture)
        this.elements = {
            header: null,
            headerPinnedLeft: null,
            headerViewport: null,
            headerRow: null,
            headerPinnedRight: null,
            body: null,
            pinnedLeft: null,
            viewport: null,
            canvas: null,
            pinnedRight: null
        };
        
        // Performance monitoring
        this.renderStartTime = 0;
        this.lastRenderDuration = 0;
        this._scrollRaf = null;
        
        // Expose AG-style APIs
        this.buildApis();

        this.init();
    }

    /**
     * Normalize pinned value to AG semantics: 'left' | 'right' | null
     */
    sanitizePinned(pinned) {
        if (pinned === 'left' || pinned === 'right') return pinned;
        if (pinned === true) return 'left';
        return null;
    }

    /**
     * Prepare columns with checkbox column if enabled
     */
    prepareColumns() {
        this.processedColumns = [...this.options.columns];

        // Enforce AG-style pinning semantics on provided columns
        this.processedColumns.forEach(col => {
            col.pinned = this.sanitizePinned(col.pinned);
            // Support AG-style hide/visible flags without breaking existing code
            if (col.visible === false) col.hide = true;
            if (col.hide === false) delete col.hide;
        });

        // Add checkbox column if enabled
        if (this.options.checkboxSelection) {
            const checkboxColumn = {
                field: 'checkbox',
                headerName: '',
                width: 50,
                pinned: 'left',
                sortable: false,
                filterable: false,
                resizable: false,
                editable: false,
                cellRenderer: this.renderCheckboxCell.bind(this),
                headerRenderer: this.renderCheckboxHeader.bind(this)
            };

            this.processedColumns.unshift(checkboxColumn);

            // Insert Preview column immediately after checkbox if not provided
            if (!this.processedColumns.find(c => c.field === 'preview')) {
                const previewColumn = {
                    field: 'preview',
                    headerName: 'Preview',
                    width: 60,
                    pinned: 'left',
                    sortable: false,
                    filterable: false,
                    resizable: false,
                    editable: false,
                    cellRenderer: () => '<div class="preview-square" title="Preview"></div>'
                };
                this.processedColumns.splice(1, 0, previewColumn);
            }
        }

        // Ensure Actions column exists at the far right pinned, without menu/sort
        if (!this.processedColumns.find(c => c.field === 'actions')) {
            const actionsColumn = {
                field: 'actions',
                headerName: 'Actions',
                width: 96,
                pinned: 'right',
                sortable: false,
                filterable: false,
                resizable: false,
                editable: false,
                cellRenderer: () => `
                    <div class="actions-container">
                        <span class="listing-edit-ic" title="Edit"><img src="assets/edit-ic.svg" alt="Edit" width="14" height="14"></span>
                        <span class="listing-analyse-ic" title="Analyse"><img src="assets/analyse-ic.svg" alt="Analyse" width="16" height="16"></span>
                    </div>
                `
            };
            this.processedColumns.push(actionsColumn);
        }

        // Sort columns by pinned status
        this.sortColumnsByPinned();

        // Ensure every column has a default width for horizontal scrolling
        this.processedColumns.forEach(col => {
            if (!col.width) {
                // Set sensible defaults; checkbox already set explicitly
                col.width = 150;
            }
        });
    }

    /**
     * Helper to sort processedColumns by pinned status (left -> center -> right)
     */
    sortColumnsByPinned() {
        this.processedColumns.sort((a, b) => {
            const aPinned = a.pinned === 'left' ? 0 : a.pinned === 'right' ? 2 : 1;
            const bPinned = b.pinned === 'left' ? 0 : b.pinned === 'right' ? 2 : 1;
            return aPinned - bPinned;
        });
    }

    /**
     * Build gridApi and columnApi with AG-style methods
     */
    buildApis() {
        const self = this;

        // Column API
        this.columnApi = {
            // Pin/unpin a column
            setColumnPinned(field, pinned) {
                return self.setColumnPinned(field, pinned);
            },
            // Apply an array of column state entries
            applyColumnState(params = {}) {
                return self.applyColumnState(params);
            },
            // Get current column state
            getColumnState() {
                return self.getColumnState();
            },
            // Set column width
            setColumnWidth(field, width) {
                return self.setColumnWidth(field, width);
            },
            // Move column to target visible index (center block)
            moveColumnByField(field, toIndex) {
                return self.moveColumnByField(field, toIndex);
            },
            // Show/hide column
            setColumnVisible(field, visible) {
                return self.setColumnVisible(field, visible);
            },
            // Accessors
            getAllColumns() { return [...self.processedColumns]; },
            getColumn(field) { return self.getColumn(field); }
        };

        // Grid API
        this.gridApi = {
            setRowData(data) { return self.setRowData(data); },
            refreshCells(params = {}) { return self.refreshCells(params); },
            redrawRows(params = {}) { return self.redrawRows(params); },
            ensureColumnVisible(field) { return self.ensureColumnVisible(field); },
            ensureIndexVisible(index) { return self.ensureIndexVisible(index); },
            sizeColumnsToFit() { return self.sizeColumnsToFit(); },
            getDisplayedRowCount() { return self.sortedData.length; },
            getDisplayedRowAtIndex(i) { return self.sortedData[i] || null; }
        };
    }

    /**
     * Render checkbox cell
     */
    renderCheckboxCell(value, column, rowData, rowIndex) {
        const isSelected = this.selectedRows.has(rowIndex);
        const iconName = isSelected ? 'checkbox-ic.svg' : 'uncheckedbox-ic.svg';

        return `
            <div class="snap-grid-checkbox-cell" data-row-index="${rowIndex}">
                <img src="assets/${iconName}"
                     alt="${isSelected ? 'Selected' : 'Not selected'}"
                     class="snap-grid-checkbox-icon">
            </div>
        `;
    }

    /**
     * Render checkbox header
     */
    renderCheckboxHeader() {
        let iconName = 'uncheckedbox-ic.svg';

        if (this.allRowsSelected) {
            iconName = 'checkbox-ic.svg';
        } else if (this.indeterminateSelection) {
            iconName = 'indeterminate-ic.svg';
        }

        return `
            <div class="snap-grid-checkbox-header">
                <img src="assets/${iconName}"
                     alt="Select all"
                     class="snap-grid-checkbox-icon">
            </div>
        `;
    }

    /**
     * Initialize the grid
     */
    init() {
        console.log('🔧 Initializing SnapGrid...');
        this.renderStartTime = performance.now();

        this.setupDOM();
        this.setupEventListeners();
        this.processData();
        this.render();

        const initDuration = performance.now() - this.renderStartTime;
        console.log(`✅ SnapGrid initialized in ${initDuration.toFixed(2)}ms`);
    }

    /**
     * Set up the DOM structure
     */
    setupDOM() {
        // Clear container
        this.container.innerHTML = '';
        this.container.className = `snap-grid ${this.options.theme}`;

        // Add ARIA attributes for accessibility
        this.container.setAttribute('role', 'grid');
        this.container.setAttribute('aria-label', 'Data grid');
        this.container.setAttribute('tabindex', '0');

        // Create main grid structure
        this.gridElement = document.createElement('div');
        this.gridElement.className = 'snap-grid-container';

        // Create controls header (styled, no legacy logic)
        this.createControlsHeader();

        // Create header with three-section layout (old grid architecture)
        if (this.options.showHeader) {
            this.headerElement = document.createElement('div');
            this.headerElement.className = 'snap-grid-header';
            this.headerElement.setAttribute('role', 'rowgroup');

            // Create header sections
            const headerPinnedLeft = document.createElement('div');
            headerPinnedLeft.className = 'snap-grid-header-pinned-left';

            const headerViewport = document.createElement('div');
            headerViewport.className = 'snap-grid-header-viewport';

            const headerRow = document.createElement('div');
            headerRow.className = 'snap-grid-header-row';
            headerRow.setAttribute('role', 'row');
            headerViewport.appendChild(headerRow);

            const headerPinnedRight = document.createElement('div');
            headerPinnedRight.className = 'snap-grid-header-pinned-right';

            this.headerElement.appendChild(headerPinnedLeft);
            this.headerElement.appendChild(headerViewport);
            this.headerElement.appendChild(headerPinnedRight);

            this.gridElement.appendChild(this.headerElement);

            // Cache header elements
            this.elements.header = this.headerElement;
            this.elements.headerPinnedLeft = headerPinnedLeft;
            this.elements.headerViewport = headerViewport;
            this.elements.headerRow = headerRow;
            this.elements.headerPinnedRight = headerPinnedRight;
        }

        // Create body with three-section layout (old grid architecture)
        this.bodyElement = document.createElement('div');
        this.bodyElement.className = 'snap-grid-body';
        this.bodyElement.setAttribute('role', 'rowgroup');

        // Create body sections
        const pinnedLeft = document.createElement('div');
        pinnedLeft.className = 'snap-grid-pinned-left';

        this.viewportElement = document.createElement('div');
        this.viewportElement.className = 'snap-grid-viewport';

        const canvas = document.createElement('div');
        canvas.className = 'snap-grid-canvas';
        this.viewportElement.appendChild(canvas);

        const pinnedRight = document.createElement('div');
        pinnedRight.className = 'snap-grid-pinned-right';

        this.bodyElement.appendChild(pinnedLeft);
        this.bodyElement.appendChild(this.viewportElement);
        this.bodyElement.appendChild(pinnedRight);

        this.gridElement.appendChild(this.bodyElement);

        // Cache body elements
        this.elements.body = this.bodyElement;
        this.elements.pinnedLeft = pinnedLeft;
        this.elements.viewport = this.viewportElement;
        this.elements.canvas = canvas;
        this.elements.pinnedRight = pinnedRight;

        // Add dedicated horizontal scrollbar bar below the body
        this.hScrollElement = document.createElement('div');
        this.hScrollElement.className = 'snap-grid-hscroll';
        this.hScrollInner = document.createElement('div');
        this.hScrollInner.className = 'snap-grid-hscroll-inner';
        this.hScrollElement.appendChild(this.hScrollInner);
        this.gridElement.appendChild(this.hScrollElement);

        // Create footer stats bar
        this.createFooter();
        this.container.appendChild(this.gridElement);

        // Set up virtual scrolling container
        this.setupVirtualScrolling();

        // Add screen reader announcements
        this.createAriaLiveRegion();
    }

    /**
     * Create the styled controls header (filters, layout, actions, info)
     */
    createControlsHeader() {
        const controls = document.createElement('div');
        controls.className = 'snap-grid-controls-header';

        // Left group: Filters dropdown + Layout dropdown + Clear Filters
        const left = document.createElement('div');
        left.className = 'snap-grid-controls-left';

        // Filters dropdown
        const filtersDropdown = this.createFiltersDropdown();
        const filtersWrapper = document.createElement('div');
        filtersWrapper.className = 'snap-grid-filters-dropdown';
        filtersWrapper.appendChild(filtersDropdown);
        left.appendChild(filtersWrapper);

        // Layout dropdown
        const layoutDropdown = this.createLayoutDropdown();
        const layoutWrapper = document.createElement('div');
        layoutWrapper.className = 'snap-grid-layout-dropdown';
        layoutWrapper.appendChild(layoutDropdown);
        left.appendChild(layoutWrapper);

        // Clear filters button
        const clearBtn = document.createElement('button');
        clearBtn.className = 'snap-grid-clear-filters-btn';
        clearBtn.textContent = 'Clear Filters';
        clearBtn.addEventListener('click', () => {
            this.clearFilters();
            if (typeof this.options.onClearFilters === 'function') {
                this.options.onClearFilters();
            }
        });
        left.appendChild(clearBtn);

        // Center group: Delete + Export
        const center = document.createElement('div');
        center.className = 'snap-grid-controls-center';

        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'snap-grid-delete-btn';
        deleteBtn.title = 'Delete Selected';
        deleteBtn.innerHTML = '<img src="assets/delete-ic.svg" alt="Delete" />';
        deleteBtn.addEventListener('click', () => {
            if (typeof this.options.onDelete === 'function') {
                this.options.onDelete(this.getSelectedData());
            }
        });
        center.appendChild(deleteBtn);

        const exportBtn = document.createElement('button');
        exportBtn.className = 'snap-grid-export-btn';
        exportBtn.title = 'Export Data';
        exportBtn.innerHTML = '<img src="assets/export-ic.svg" alt="Export" />';
        exportBtn.addEventListener('click', () => this.exportToCsv());
        center.appendChild(exportBtn);

        // Right group: Loaded info pill
        const right = document.createElement('div');
        right.className = 'snap-grid-controls-right';
        const loadedInfo = document.createElement('div');
        loadedInfo.className = 'snap-grid-loaded-info';
        right.appendChild(loadedInfo);

        controls.appendChild(left);
        controls.appendChild(center);
        controls.appendChild(right);

        this.gridElement.appendChild(controls);

        // Cache references
        this.controlsHeaderElement = controls;
        this.controls = {
            clearBtn,
            deleteBtn,
            exportBtn,
            loadedInfo,
            filtersDropdown,
            layoutDropdown
        };
    }

    /**
     * Create footer stats bar
     */
    createFooter() {
        if (!this.options.showFooter) return;

        const footer = document.createElement('div');
        footer.className = 'snap-grid-footer';

        const stats = document.createElement('div');
        stats.className = 'snap-grid-footer-stats';

        const makeStat = (label) => {
            const item = document.createElement('div');
            item.className = 'snap-grid-stat-item';
            const lab = document.createElement('div');
            lab.className = 'snap-grid-stat-label';
            lab.textContent = label;
            const val = document.createElement('div');
            val.className = 'snap-grid-stat-value';
            val.textContent = '0';
            item.appendChild(lab);
            item.appendChild(val);
            return { item, val };
        };

        const rows = makeStat('ROWS');
        const filtered = makeStat('FILTERED');
        const selected = makeStat('SELECTED');

        stats.appendChild(rows.item);
        stats.appendChild(filtered.item);
        stats.appendChild(selected.item);
        footer.appendChild(stats);
        this.gridElement.appendChild(footer);

        this.footerElement = footer;
        this.footer = { rows: rows.val, filtered: filtered.val, selected: selected.val };
    }

    /**
     * Generic dropdown builder with styles compatible to .snap-dropdown
     */
    createDropdown({ className = '', items = [], selectedValue = null, onChange = null } = {}) {
        const dropdown = document.createElement('div');
        dropdown.className = `snap-dropdown ${className}`.trim();

        const header = document.createElement('div');
        header.className = 'dropdown-header';
        const title = document.createElement('span');
        title.textContent = '';
        const caret = document.createElement('img');
        caret.src = 'assets/dropdown-ic.svg';
        caret.alt = 'Open';
        header.appendChild(title);
        header.appendChild(caret);

        const menu = document.createElement('div');
        menu.className = 'dropdown-menu hidden';

        const setSelected = (value, text) => {
            title.textContent = text;
            selectedValue = value;
            // Update selected class
            menu.querySelectorAll('.dropdown-item').forEach(el => {
                if (el.dataset.value === String(value)) el.classList.add('selected');
                else el.classList.remove('selected');
            });
        };

        items.forEach(item => {
            if (item.divider) {
                const div = document.createElement('div');
                div.className = 'dropdown-divider';
                menu.appendChild(div);
                return;
            }
            const opt = document.createElement('div');
            opt.className = 'dropdown-item';
            opt.dataset.value = String(item.value);
            if (item.icon) {
                const icon = document.createElement('span');
                icon.className = 'item-icon';
                icon.innerHTML = `<img src="assets/${item.icon}" alt="" />`;
                opt.appendChild(icon);
            }
            const text = document.createElement('span');
            text.className = 'item-text';
            text.textContent = item.text;
            opt.appendChild(text);
            opt.addEventListener('click', (e) => {
                e.stopPropagation();
                setSelected(item.value, item.text);
                menu.classList.add('hidden');
                dropdown.classList.remove('focused');
                if (typeof onChange === 'function') onChange(item.value, item);
                // Dispatch event for external listeners
                dropdown.dispatchEvent(new CustomEvent('change', { detail: { value: item.value, text: item.text, item } }));
            });
            menu.appendChild(opt);
        });

        header.addEventListener('click', (e) => {
            e.stopPropagation();
            const open = !menu.classList.contains('hidden');
            // Close others
            document.querySelectorAll('.snap-dropdown.focused').forEach(d => {
                if (d !== dropdown) {
                    d.classList.remove('focused');
                    const m = d.querySelector('.dropdown-menu');
                    if (m) m.classList.add('hidden');
                }
            });
            if (!open) {
                dropdown.classList.add('focused');
                menu.classList.remove('hidden');
            } else {
                dropdown.classList.remove('focused');
                menu.classList.add('hidden');
            }
        });

        document.addEventListener('click', () => {
            dropdown.classList.remove('focused');
            menu.classList.add('hidden');
        });

        dropdown.appendChild(header);
        dropdown.appendChild(menu);

        // Initialize selection
        const initItem = items.find(i => i.value === selectedValue) || items.find(i => !i.divider) || null;
        if (initItem) setSelected(initItem.value, initItem.text);

        return dropdown;
    }

    /**
     * Filters dropdown: includes "All" and any provided presets
     */
    createFiltersDropdown() {
        const presets = Array.isArray(this.options.filterPresets) ? this.options.filterPresets : [];
        const items = [
            { value: 'all', text: 'All' },
            ...(presets.length ? [{ divider: true }] : []),
            ...presets.map(p => ({ value: p.value ?? p, text: p.text ?? String(p), icon: p.icon }))
        ];
        const dropdown = this.createDropdown({ className: '', items, selectedValue: 'all', onChange: (val) => {
            if (val === 'all') {
                this.clearFilters();
                if (typeof this.options.onFilterPresetSelected === 'function') this.options.onFilterPresetSelected(val);
                return;
            }
            if (typeof this.options.onFilterPresetSelected === 'function') {
                this.options.onFilterPresetSelected(val);
            }
        } });
        return dropdown;
    }

    /**
     * Layout dropdown: Default, Save, Delete + optional saved layouts (callback-based)
     */
    createLayoutDropdown() {
        const baseItems = [
            { value: 'default', text: 'Default Layout', icon: 'checked-option-ic.svg' },
            { divider: true },
            { value: 'save', text: 'Save Layout', icon: 'save-layout-ic.svg' },
            { value: 'delete', text: 'Delete Selected', icon: 'delete-ic.svg' }
        ];
        const savedLayouts = Array.isArray(this.options.savedLayouts) ? this.options.savedLayouts : [];
        if (savedLayouts.length) {
            baseItems.splice(2, 0, ...savedLayouts.map(l => ({ value: l.id || l.value, text: l.name || l.text, icon: 'checked-option-ic.svg' })));
        }

        const dropdown = this.createDropdown({ className: 'layout-dropdown', items: baseItems, selectedValue: 'default', onChange: (val, item) => {
            if (val === 'default') {
                if (typeof this.options.onApplyDefaultLayout === 'function') this.options.onApplyDefaultLayout();
            } else if (val === 'save') {
                if (typeof this.options.onSaveLayout === 'function') this.options.onSaveLayout(this.getCurrentLayout());
            } else if (val === 'delete') {
                if (typeof this.options.onDeleteLayout === 'function') this.options.onDeleteLayout(this.currentLayoutId || null);
            } else {
                this.currentLayoutId = val;
                if (typeof this.options.onApplyLayout === 'function') this.options.onApplyLayout(val);
            }
        } });
        return dropdown;
    }

    /**
     * Capture current layout (order and widths)
     */
    getCurrentLayout() {
        return {
            columns: this.processedColumns.map(c => ({ field: c.field, width: c.width || null, pinned: c.pinned || null }))
        };
    }

    /**
     * Set up virtual scrolling
     */
    setupVirtualScrolling() {
        if (!this.options.virtualScrolling) return;
        
        // Create scrollable container
        this.scrollContainer = document.createElement('div');
        this.scrollContainer.className = 'snap-grid-scroll-container';
        this.bodyElement.appendChild(this.scrollContainer);
        
        // Calculate total height
        this.updateVirtualScrolling();
    }

    /**
     * Update virtual scrolling calculations
     */
    updateVirtualScrolling() {
        if (!this.options.virtualScrolling) return;
        
        const rowCount = this.sortedData.length;
        this.totalHeight = rowCount * this.options.rowHeight;
        
        if (this.scrollContainer) {
            this.scrollContainer.style.height = `${this.totalHeight}px`;
        }
        
        this.calculateVisibleRange();
    }

    /**
     * Calculate which rows should be visible
     */
    calculateVisibleRange() {
        if (!this.options.virtualScrolling) {
            this.visibleStartIndex = 0;
            this.visibleEndIndex = this.sortedData.length;
            return;
        }
        
        // Check if bodyElement is initialized (similar to AG Grid's null checks)
        if (!this.bodyElement) {
            this.visibleStartIndex = 0;
            this.visibleEndIndex = this.sortedData.length;
            return;
        }
        
        const containerHeight = this.bodyElement.clientHeight;
        const rowHeight = this.options.rowHeight;
        const bufferSize = this.options.bufferSize;
        
        this.visibleStartIndex = Math.max(0, Math.floor(this.scrollTop / rowHeight) - bufferSize);
        this.visibleEndIndex = Math.min(
            this.sortedData.length,
            Math.ceil((this.scrollTop + containerHeight) / rowHeight) + bufferSize
        );
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Scroll sync and virtualization
        if (this.bodyElement) {
            try {
                this.bodyElement.addEventListener('scroll', this.handleScroll.bind(this), { passive: true });
            } catch (e) {
                // Fallback for older browsers
                this.bodyElement.addEventListener('scroll', this.handleScroll.bind(this));
            }
        }

        // Horizontal scrollbar syncing
        if (this.hScrollElement) {
            try {
                this.hScrollElement.addEventListener('scroll', this.handleHScroll.bind(this), { passive: true });
            } catch (e) {
                this.hScrollElement.addEventListener('scroll', this.handleHScroll.bind(this));
            }
        }
        
        // Window resize
        window.addEventListener('resize', this.handleResize.bind(this));
        
        // Click events (delegated)
        this.container.addEventListener('click', this.handleClick.bind(this));
        
        // Keyboard events
        this.container.addEventListener('keydown', this.handleKeydown.bind(this));
        
        // Column resize events
        if (this.options.resizable) {
            this.setupColumnResize();
        }

        // Column dragging events
        if (this.options.columnDragging) {
            this.container.addEventListener('dragstart', this.handleDragStart.bind(this));
            this.container.addEventListener('dragover', this.handleDragOver.bind(this));
            this.container.addEventListener('drop', this.handleDrop.bind(this));
            this.container.addEventListener('dragend', this.handleDragEnd.bind(this));
        }
    }

    /**
     * Handle scroll events for virtual scrolling
     */
    handleScroll(event) {
        const target = event.target;
        const newLeft = target.scrollLeft || 0;
        const newTop = target.scrollTop || 0;

        // Horizontal: keep header and bottom bar in sync
        if (newLeft !== this.scrollLeft) {
            this.scrollLeft = newLeft;
            if (this.hScrollElement && this.hScrollElement.scrollLeft !== newLeft) {
                this.hScrollElement.scrollLeft = newLeft;
            }
            this.syncHorizontalScroll();
        }

        // Vertical: only update virtualization if enabled and changed
        if (this.options.virtualScrolling && newTop !== this.scrollTop) {
            this.scrollTop = newTop;
            if (!this._scrollRaf) {
                this._scrollRaf = requestAnimationFrame(() => {
                    this._scrollRaf = null;
                    this.calculateVisibleRange();
                    this.renderRows();
                    this.updateHeaderFooterStats();
                });
            }
        }
    }

    /**
     * Handle dedicated horizontal scrollbar scroll events
     */
    handleHScroll(event) {
        const x = this.hScrollElement ? (this.hScrollElement.scrollLeft || 0) : 0;
        if (x !== this.scrollLeft) {
            this.scrollLeft = x;
            if (this.bodyElement && this.bodyElement.scrollLeft !== x) {
                this.bodyElement.scrollLeft = x;
            }
            this.syncHorizontalScroll();
        }
    }

    /**
     * Handle window resize
     */
    handleResize() {
        this.calculateVisibleRange();
        this.render();
        this.updateHorizontalMetrics();
        this.syncHorizontalScroll();
    }

    /**
     * Handle click events
     */
    handleClick(event) {
        const target = event.target;

        // Checkbox click handling
        if (target.closest('.snap-grid-checkbox-cell')) {
            this.handleCheckboxClick(event);
            return;
        }

        // Header checkbox click
        if (target.closest('.snap-grid-checkbox-header')) {
            this.handleHeaderCheckboxClick(event);
            return;
        }

        // Header click for sorting
        if (target.closest('.snap-grid-header-cell')) {
            this.handleHeaderClick(event);
        }

        // Row click for selection
        if (target.closest('.snap-grid-row')) {
            this.handleRowClick(event);
        }

        // Cell click for editing
        if (target.closest('.snap-grid-cell')) {
            this.handleCellClick(event);
        }
    }

    /**
     * Handle keyboard events
     */
    handleKeydown(event) {
        // Arrow key navigation
        if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
            this.handleArrowKeys(event);
        }
        
        // Enter for editing
        if (event.key === 'Enter') {
            this.handleEnterKey(event);
        }
        
        // Escape to cancel editing
        if (event.key === 'Escape') {
            this.handleEscapeKey(event);
        }

        // Ctrl/Cmd + A: Select all
        if ((event.ctrlKey || event.metaKey) && event.key === 'a') {
            event.preventDefault();
            this.toggleAllRowsSelection();
        }
    }

    /**
     * Handle drag start for column dragging
     */
    handleDragStart(event) {
        const headerCell = event.target.closest('.snap-grid-header-cell');
        if (!headerCell || !headerCell.draggable) return;

        const columnIndex = parseInt(headerCell.dataset.index);
        const column = this.processedColumns[columnIndex];

        // Don't allow dragging pinned columns
        if (column.pinned) {
            event.preventDefault();
            return;
        }

        this.isDraggingColumn = true;
        this.draggedColumn = columnIndex;
        this.dragStartX = event.clientX;
        this.dragStartIndex = columnIndex;

        headerCell.classList.add('dragging');

        // Set drag data
        event.dataTransfer.effectAllowed = 'move';
        event.dataTransfer.setData('text/plain', columnIndex.toString());

        this.announceToScreenReader(`Started dragging column ${column.headerName || column.field}`);
    }

    /**
     * Handle drag over for column dragging
     */
    handleDragOver(event) {
        if (!this.isDraggingColumn) return;

        event.preventDefault();
        event.dataTransfer.dropEffect = 'move';

        const headerCell = event.target.closest('.snap-grid-header-cell');
        if (!headerCell) return;

        const targetIndex = parseInt(headerCell.dataset.index);
        const targetColumn = this.processedColumns[targetIndex];

        // Don't allow dropping on pinned columns
        if (targetColumn.pinned) return;

        // Remove previous drag-over indicators
        document.querySelectorAll('.snap-grid-header-cell.drag-over').forEach(cell => {
            cell.classList.remove('drag-over');
        });

        // Add drag-over indicator
        if (targetIndex !== this.draggedColumn) {
            headerCell.classList.add('drag-over');
        }
    }

    /**
     * Handle drop for column dragging
     */
    handleDrop(event) {
        if (!this.isDraggingColumn) return;

        event.preventDefault();

        const headerCell = event.target.closest('.snap-grid-header-cell');
        if (!headerCell) return;

        const targetIndex = parseInt(headerCell.dataset.index);
        const targetColumn = this.processedColumns[targetIndex];

        // Don't allow dropping on pinned columns
        if (targetColumn.pinned || targetIndex === this.draggedColumn) return;

        // Move column
        this.moveColumn(this.draggedColumn, targetIndex);

        // Trigger callback
        if (this.options.onColumnMoved) {
            this.options.onColumnMoved(this.draggedColumn, targetIndex);
        }
    }

    /**
     * Handle drag end for column dragging
     */
    handleDragEnd(event) {
        this.isDraggingColumn = false;

        // Remove all drag-related classes
        document.querySelectorAll('.snap-grid-header-cell.dragging').forEach(cell => {
            cell.classList.remove('dragging');
        });

        document.querySelectorAll('.snap-grid-header-cell.drag-over').forEach(cell => {
            cell.classList.remove('drag-over');
        });

        this.draggedColumn = null;
        this.dragStartX = 0;
        this.dragStartIndex = 0;
    }

    /**
     * Move column from one index to another
     */
    moveColumn(fromIndex, toIndex) {
        if (fromIndex === toIndex) return;

        // Move column in processedColumns array
        const column = this.processedColumns.splice(fromIndex, 1)[0];
        this.processedColumns.splice(toIndex, 0, column);

        // Re-render the grid
        this.render();

        const columnName = column.headerName || column.field;
        this.announceToScreenReader(`Column ${columnName} moved from position ${fromIndex + 1} to position ${toIndex + 1}`);
    }

    /**
     * Process and prepare data
     */
    processData() {
        // Apply filters
        this.applyFilters();
        
        // Apply sorting
        this.applySorting();
        
        // Update virtual scrolling
        this.updateVirtualScrolling();
    }

    /**
     * Apply current filters to data
     */
    applyFilters() {
        this.filteredData = this.data.filter(row => {
            return Object.entries(this.filterState).every(([field, filter]) => {
                const value = row[field];
                return this.matchesFilter(value, filter);
            });
        });
    }

    /**
     * Check if value matches filter criteria
     */
    matchesFilter(value, filter) {
        if (!filter || !filter.value) return true;
        
        const filterValue = filter.value.toLowerCase();
        const cellValue = String(value || '').toLowerCase();
        
        switch (filter.type) {
            case 'contains':
                return cellValue.includes(filterValue);
            case 'equals':
                return cellValue === filterValue;
            case 'startsWith':
                return cellValue.startsWith(filterValue);
            case 'endsWith':
                return cellValue.endsWith(filterValue);
            default:
                return cellValue.includes(filterValue);
        }
    }

    /**
     * Apply current sorting to filtered data
     */
    applySorting() {
        this.sortedData = [...this.filteredData];
        
        Object.entries(this.sortState).forEach(([field, direction]) => {
            this.sortedData.sort((a, b) => {
                const aVal = a[field];
                const bVal = b[field];
                
                let comparison = 0;
                if (aVal < bVal) comparison = -1;
                if (aVal > bVal) comparison = 1;
                
                return direction === 'desc' ? -comparison : comparison;
            });
        });
    }

    /**
     * Main render method
     */
    render() {
        this.renderStartTime = performance.now();

        if (this.options.showHeader) {
            this.renderHeader();
        }

        this.renderRows();

        // Update header/footer stats if present
        this.updateHeaderFooterStats();

        this.lastRenderDuration = performance.now() - this.renderStartTime;
        if (this.lastRenderDuration > 16) { // More than one frame
            console.warn(`⚠️ SnapGrid render took ${this.lastRenderDuration.toFixed(2)}ms`);
        }
    }

    /**
     * Render the header row
     */
    renderHeader() {
        if (!this.headerElement || !this.elements.headerPinnedLeft || !this.elements.headerRow || !this.elements.headerPinnedRight) return;

        // Clear all header sections
        this.elements.headerPinnedLeft.innerHTML = '';
        this.elements.headerRow.innerHTML = '';
        this.elements.headerPinnedRight.innerHTML = '';

        // Set height for all header sections
        const height = `${this.options.headerHeight}px`;
        this.elements.headerPinnedLeft.style.height = height;
        this.elements.headerViewport.style.height = height;
        this.elements.headerPinnedRight.style.height = height;

        this.processedColumns.forEach((column, index) => {
            const headerCell = document.createElement('div');
            headerCell.className = 'snap-grid-header-cell';
            headerCell.dataset.field = column.field;
            headerCell.dataset.index = index;

            // Add pinned class
            if (column.pinned) {
                headerCell.classList.add(`pinned-${column.pinned}`);
                // Apply offset so pinned columns don’t overlap
                if (column.pinned === 'left') {
                    const off = this.pinnedLeftOffsets[index] || 0;
                    headerCell.style.left = off + 'px';
                } else if (column.pinned === 'right') {
                    const off = (this.pinnedRightOffsets[index] || 0);
                    // Header compensates vertical scrollbar via padding-right
                    headerCell.style.right = `${off}px`;
                }
            }

            // Add draggable attributes for non-pinned columns
            if (this.options.columnDragging && !column.pinned) {
                headerCell.draggable = true;
                headerCell.classList.add('draggable');
            }

            // Add accessibility attributes
            this.addHeaderAccessibility(headerCell, column, index);

            // Set width
            if (column.width) {
                const w = (column.hide ? 0 : column.width);
                headerCell.style.width = `${w}px`;
                headerCell.style.minWidth = `${w}px`;
            }

            // Hidden columns: keep DOM for indexing/drag, but not visible
            if (column.hide) {
                headerCell.style.display = 'none';
            }

            // Header content
            const headerContent = document.createElement('div');
            headerContent.className = 'snap-grid-header-content';

            // Use custom header renderer if available
            if (column.headerRenderer && typeof column.headerRenderer === 'function') {
                const customHeader = column.headerRenderer();
                if (typeof customHeader === 'string') {
                    headerContent.innerHTML = customHeader;
                } else {
                    headerContent.appendChild(customHeader);
                }
            } else {
                const headerText = document.createElement('span');
                headerText.className = 'snap-grid-header-text';
                headerText.textContent = column.headerName || column.field;
                headerContent.appendChild(headerText);
            }

            // Sort indicator (skip for checkbox/preview/actions)
            if (this.options.sortable && column.sortable !== false && !['checkbox','preview','actions'].includes(column.field)) {
                const sortIcon = document.createElement('span');
                sortIcon.className = 'snap-grid-sort-icon';

                const sortDirection = this.sortState[column.field];
                if (sortDirection) {
                    sortIcon.classList.add(`sort-${sortDirection}`);
                    const iconName = sortDirection === 'asc' ? 'ascending-ic.svg' : 'descending-ic.svg';
                    sortIcon.innerHTML = `<img src="assets/${iconName}" alt="${sortDirection === 'asc' ? 'Ascending' : 'Descending'}" class="sort-icon-img">`;
                }

                headerContent.appendChild(sortIcon);
            }

            // Column menu button (only if column allows filtering/sorting and not special fields)
            if (((this.options.filterable && column.filterable !== false) || (this.options.sortable && column.sortable !== false))
                && !['checkbox','preview','actions'].includes(column.field)) {
                const menuButton = document.createElement('button');
                menuButton.className = 'snap-grid-column-menu-btn';
                menuButton.innerHTML = '⋮';
                menuButton.setAttribute('aria-label', `Column menu for ${column.headerName || column.field}`);
                headerContent.appendChild(menuButton);
            }

            headerCell.appendChild(headerContent);

            // Resize handle
            if (this.options.resizable && column.resizable !== false) {
                const resizeHandle = document.createElement('div');
                resizeHandle.className = 'snap-grid-resize-handle';
                headerCell.appendChild(resizeHandle);
            }

            // Distribute header cells to appropriate sections
            if (column.pinned === 'left') {
                this.elements.headerPinnedLeft.appendChild(headerCell);
            } else if (column.pinned === 'right') {
                this.elements.headerPinnedRight.appendChild(headerCell);
            } else {
                this.elements.headerRow.appendChild(headerCell);
            }
        });
    }

    /**
     * Create a header cell element
     */
    createHeaderCell(column, index) {
        const headerCell = document.createElement('div');
        headerCell.className = 'snap-grid-header-cell';
        headerCell.dataset.field = column.field;
        headerCell.dataset.index = index;

        // Add draggable attributes for non-pinned columns
        if (this.options.columnDragging && !column.pinned) {
            headerCell.draggable = true;
            headerCell.classList.add('draggable');
        }

        // Add accessibility attributes
        this.addHeaderAccessibility(headerCell, column, index);

        // Set width
        if (column.width) {
            const w = (column.hide ? 0 : column.width);
            headerCell.style.width = `${w}px`;
            headerCell.style.minWidth = `${w}px`;
        }

        // Hidden columns: keep DOM for indexing/drag, but not visible
        if (column.hide) {
            headerCell.style.display = 'none';
        }

        // Header content
        const headerContent = document.createElement('div');
        headerContent.className = 'snap-grid-header-content';

        // Use custom header renderer if available
        if (column.headerRenderer && typeof column.headerRenderer === 'function') {
            const customHeader = column.headerRenderer();
            if (typeof customHeader === 'string') {
                headerContent.innerHTML = customHeader;
            } else {
                headerContent.appendChild(customHeader);
            }
        } else {
            const headerText = document.createElement('span');
            headerText.className = 'snap-grid-header-text';
            headerText.textContent = column.headerName || column.field;
            headerContent.appendChild(headerText);
        }

        // Sort indicator (skip for checkbox/preview/actions)
        if (this.options.sortable && column.sortable !== false && !['checkbox','preview','actions'].includes(column.field)) {
            const sortIcon = document.createElement('span');
            sortIcon.className = 'snap-grid-sort-icon';

            const sortDirection = this.sortState[column.field];
            if (sortDirection) {
                sortIcon.classList.add(`sort-${sortDirection}`);
                const iconName = sortDirection === 'asc' ? 'ascending-ic.svg' : 'descending-ic.svg';
                sortIcon.innerHTML = `<img src="assets/${iconName}" alt="${sortDirection === 'asc' ? 'Ascending' : 'Descending'}" class="sort-icon-img">`;
            }

            headerContent.appendChild(sortIcon);
        }

        // Column menu button (only if column allows filtering/sorting and not special fields)
        if (((this.options.filterable && column.filterable !== false) || (this.options.sortable && column.sortable !== false))
            && !['checkbox','preview','actions'].includes(column.field)) {
            const menuButton = document.createElement('button');
            menuButton.className = 'snap-grid-column-menu-btn';
            menuButton.innerHTML = '⋮';
            menuButton.setAttribute('aria-label', `Column menu for ${column.headerName || column.field}`);
            headerContent.appendChild(menuButton);
        }

        headerCell.appendChild(headerContent);

        // Resize handle
        if (this.options.resizable && column.resizable !== false) {
            const resizeHandle = document.createElement('div');
            resizeHandle.className = 'snap-grid-resize-handle';
            headerCell.appendChild(resizeHandle);
        }

        return headerCell;
    }

    /**
     * Handle scroll events from pinned columns - sync with main viewport
     */
    handlePinnedScroll(event) {
        // Sync the main viewport with pinned column scroll
        const scrollTop = event.target.scrollTop;
        if (this.elements.viewport && this.elements.viewport.scrollTop !== scrollTop) {
            this.elements.viewport.scrollTop = scrollTop;
        }
    }

    /**
     * Compensate for scrollbar width in header to maintain column alignment
     */
    compensateHeaderScrollbar() {
        if (!this.elements.headerViewport || !this.elements.viewport) return;

        // Calculate if there's a vertical scrollbar in the body viewport
        const hasVerticalScrollbar = this.elements.viewport.scrollHeight > this.elements.viewport.clientHeight;

        if (hasVerticalScrollbar) {
            // Get the scrollbar width by comparing offsetWidth and clientWidth
            const scrollbarWidth = this.elements.viewport.offsetWidth - this.elements.viewport.clientWidth;

            // Apply padding-right to header viewport to compensate for scrollbar
            this.elements.headerViewport.style.paddingRight = `${scrollbarWidth}px`;
        } else {
            // Remove padding if no scrollbar
            this.elements.headerViewport.style.paddingRight = '0px';
        }
    }

    /**
     * Compensate for horizontal scrollbar height in pinned columns to maintain row alignment
     */
    compensatePinnedColumnsHeight() {
        if (!this.elements.pinnedLeft || !this.elements.pinnedRight || !this.elements.viewport) return;

        // Calculate if there's a horizontal scrollbar in the main viewport
        const hasHorizontalScrollbar = this.elements.viewport.scrollWidth > this.elements.viewport.clientWidth;

        if (hasHorizontalScrollbar) {
            // Get the scrollbar height by comparing offsetHeight and clientHeight
            const scrollbarHeight = this.elements.viewport.offsetHeight - this.elements.viewport.clientHeight;

            // Apply height compensation to pinned columns to match the main viewport's available height
            const compensatedHeight = `calc(100% - ${scrollbarHeight}px)`;
            this.elements.pinnedLeft.style.height = compensatedHeight;
            this.elements.pinnedRight.style.height = compensatedHeight;

            console.log('🔧 Compensated pinned columns height for horizontal scrollbar:', scrollbarHeight, 'px');
        } else {
            // Reset to full height when no horizontal scrollbar
            this.elements.pinnedLeft.style.height = '100%';
            this.elements.pinnedRight.style.height = '100%';
        }
    }

    /**
     * Render visible rows
     */
    renderRows() {
        if (!this.elements.canvas || !this.elements.pinnedLeft || !this.elements.pinnedRight) return;

        // Clear all body sections
        this.elements.canvas.innerHTML = '';
        this.elements.pinnedLeft.innerHTML = '';
        this.elements.pinnedRight.innerHTML = '';

        // Create fragments for each section
        const canvasFragment = document.createDocumentFragment();
        const pinnedLeftFragment = document.createDocumentFragment();
        const pinnedRightFragment = document.createDocumentFragment();

        for (let i = this.visibleStartIndex; i < this.visibleEndIndex; i++) {
            const rowData = this.sortedData[i];
            if (!rowData) continue;

            const rowSections = this.createRowSections(rowData, i);

            if (rowSections.left) pinnedLeftFragment.appendChild(rowSections.left);
            if (rowSections.center) canvasFragment.appendChild(rowSections.center);
            if (rowSections.right) pinnedRightFragment.appendChild(rowSections.right);
        }

        // Append all fragments
        this.elements.canvas.appendChild(canvasFragment);
        this.elements.pinnedLeft.appendChild(pinnedLeftFragment);
        this.elements.pinnedRight.appendChild(pinnedRightFragment);

        // Update viewport position for virtual scrolling
        if (this.options.virtualScrolling) {
            const offsetY = this.visibleStartIndex * this.options.rowHeight;

            // Apply padding to all sections for virtual scrolling
            this.elements.canvas.style.paddingTop = `${offsetY}px`;
            this.elements.pinnedLeft.style.paddingTop = `${offsetY}px`;
            this.elements.pinnedRight.style.paddingTop = `${offsetY}px`;
        } else {
            this.elements.canvas.style.paddingTop = '';
            this.elements.pinnedLeft.style.paddingTop = '';
            this.elements.pinnedRight.style.paddingTop = '';
        }

        // Compensate for scrollbars to maintain alignment
        this.compensateHeaderScrollbar();
        this.compensatePinnedColumnsHeight();
    }

    /**
     * Update counts and info pills in header/footer
     */
    updateHeaderFooterStats() {
        // Header loaded info
        if (this.controls?.loadedInfo) {
            const shown = this.sortedData.length;
            const total = this.data.length;
            this.controls.loadedInfo.textContent = `${shown.toLocaleString()} / ${total.toLocaleString()}`;
        }

        // Footer stats
        if (this.footer) {
            const total = this.data.length;
            const filtered = this.filteredData.length;
            const selected = this.selectedRows.size;
            this.footer.rows.textContent = total.toLocaleString();
            this.footer.filtered.textContent = filtered.toLocaleString();
            this.footer.selected.textContent = selected.toLocaleString();
        }
    }

    /**
     * Create row sections for the three-section layout
     */
    createRowSections(rowData, rowIndex) {
        const sections = {
            left: null,
            center: null,
            right: null
        };

        // Create containers for each section that has columns
        const leftColumns = this.processedColumns.filter(col => col.pinned === 'left');
        const centerColumns = this.processedColumns.filter(col => !col.pinned);
        const rightColumns = this.processedColumns.filter(col => col.pinned === 'right');

        if (leftColumns.length > 0) {
            sections.left = this.createRowSection(rowData, rowIndex, leftColumns, 'left');
        }
        if (centerColumns.length > 0) {
            sections.center = this.createRowSection(rowData, rowIndex, centerColumns, 'center');
        }
        if (rightColumns.length > 0) {
            sections.right = this.createRowSection(rowData, rowIndex, rightColumns, 'right');
        }

        return sections;
    }

    /**
     * Create a row section for a specific set of columns
     */
    createRowSection(rowData, rowIndex, columns, sectionType) {
        const row = document.createElement('div');
        row.className = 'snap-grid-row';
        row.dataset.index = rowIndex;
        row.style.height = `${this.options.rowHeight}px`;

        // Add row selection state
        if (this.selectedRows.has(rowData)) {
            row.classList.add('selected');
        }

        // Add hover and click handlers
        this.addRowEventHandlers(row, rowData, rowIndex);

        // Create cells for this section's columns
        columns.forEach((column, columnIndex) => {
            const cell = this.createCell(rowData, column, rowIndex, columnIndex);
            row.appendChild(cell);
        });

        return row;
    }

    /**
     * Create a single row element (legacy method for compatibility)
     */
    createRow(rowData, rowIndex) {
        // Ensure pinned offsets are up to date for cell positioning
        this.computePinnedOffsets();
        
        const row = document.createElement('div');
        row.className = 'snap-grid-row';
        row.dataset.index = rowIndex;
        row.setAttribute('role', 'row');
        row.setAttribute('aria-rowindex', rowIndex + 2); // +2 because header is row 1

        // Row selection state
        if (this.selectedRows.has(rowIndex)) {
            row.classList.add('selected');
            row.setAttribute('aria-selected', 'true');
        } else {
            row.setAttribute('aria-selected', 'false');
        }

        // Row height and width
        row.style.height = `${this.options.rowHeight}px`;
        row.style.width = `${this.getTotalColumnsWidth()}px`;

        this.processedColumns.forEach((column, columnIndex) => {
            const cell = document.createElement('div');
            cell.className = 'snap-grid-cell';
            cell.dataset.field = column.field;
            cell.dataset.row = rowIndex;
            cell.dataset.column = columnIndex;

            // Add pinned class
            if (column.pinned) {
                cell.classList.add(`pinned-${column.pinned}`);
                if (column.pinned === 'left') {
                    const off = this.pinnedLeftOffsets[columnIndex] || 0;
                    cell.style.left = off + 'px';
                } else if (column.pinned === 'right') {
                    const off = this.pinnedRightOffsets[columnIndex] || 0;
                    cell.style.right = `calc(var(--vscroll-offset, 0px) + ${off}px)`;
                }
            }

            // Add accessibility attributes
            this.addCellAccessibility(cell, column, rowData, rowIndex, columnIndex);

            // Set width to match header
            const w = (column.hide ? 0 : column.width);
            cell.style.width = `${w}px`;
            cell.style.minWidth = `${w}px`;

            // Hidden columns: keep DOM for indexing, but not visible
            if (column.hide) {
                cell.style.display = 'none';
            }

            // Cell content
            const cellValue = rowData[column.field];
            const cellContent = this.renderCellContent(cellValue, column, rowData, rowIndex);

            if (typeof cellContent === 'string') {
                cell.innerHTML = cellContent;
            } else {
                cell.appendChild(cellContent);
            }

            // Cell editing
            if (this.options.editable && column.editable !== false) {
                cell.classList.add('editable');
            }

            row.appendChild(cell);
        });

        return row;
    }

    /**
     * Sum of column widths for horizontal overflow
     */
    getTotalColumnsWidth() {
        return this.processedColumns.reduce((sum, col) => sum + (col.hide ? 0 : (col.width || 0)), 0);
    }

    /**
     * Keep header horizontally synced with body scroll
     */
    syncHorizontalScroll() {
        if (!this.headerElement || !this.bodyElement) return;
        const scroller = this.headerElement.querySelector('.snap-grid-header-scroller');
        if (scroller) {
            const x = this.hScrollElement ? (this.hScrollElement.scrollLeft || 0) : (this.bodyElement?.scrollLeft || 0);
            scroller.style.transform = `translateX(${-x}px)`;
            // Counter-translate pinned header cells so they remain fixed
            const pinnedLeft = this.headerElement.querySelectorAll('.snap-grid-header-cell.pinned-left');
            pinnedLeft.forEach(el => {
                el.style.transform = x ? `translateX(${x}px)` : '';
            });
            const pinnedRight = this.headerElement.querySelectorAll('.snap-grid-header-cell.pinned-right');
            pinnedRight.forEach(el => {
                el.style.transform = x ? `translateX(${x}px)` : '';
            });
        }
    }

    /**
     * Reconcile horizontal sizes: compute total width, compare to measured header width,
     * and apply the larger value to viewport and rows to guarantee full scroll range.
     */
    updateHorizontalMetrics() {
        if (!this.viewportElement) return;
        let total = this.getTotalColumnsWidth();

        const headerRow = this.headerElement?.querySelector('.snap-grid-header-row');
        if (headerRow) {
            const measured = headerRow.scrollWidth || headerRow.getBoundingClientRect().width;
            if (measured > total) total = measured;
        }

        // Apply to viewport
        this.viewportElement.style.width = `${total}px`;

        // Update dedicated horizontal scrollbar width
        if (this.hScrollInner) {
            this.hScrollInner.style.width = `${total}px`;
        }

        // Apply to existing rows
        const rows = this.viewportElement.querySelectorAll('.snap-grid-row');
        rows.forEach(r => { r.style.width = `${total}px`; });

        this.totalColumnsWidth = total;

        // Update vertical scrollbar gutter offset (so pinned-right is never overlapped)
        const sbw = this.getScrollbarWidth();
        if (this.container) {
            this.container.style.setProperty('--vscroll-offset', `${sbw}px`);
        }
    }

    /**
     * Compute sticky offsets for pinned columns
     */
    computePinnedOffsets() {
        this.pinnedLeftOffsets = {};
        this.pinnedRightOffsets = {};

        let left = 0;
        for (let i = 0; i < this.processedColumns.length; i++) {
            const col = this.processedColumns[i];
            if (col.pinned === 'left') {
                this.pinnedLeftOffsets[i] = left;
                if (!col.hide) left += (col.width || 0);
            }
        }

        let right = 0;
        for (let i = this.processedColumns.length - 1; i >= 0; i--) {
            const col = this.processedColumns[i];
            if (col.pinned === 'right') {
                this.pinnedRightOffsets[i] = right;
                if (!col.hide) right += (col.width || 0);
            }
        }
    }

    /**
     * Column helpers and API methods
     */
    getColumn(field) {
        return this.processedColumns.find(c => c.field === field) || null;
    }

    getColumnIndex(field) {
        return this.processedColumns.findIndex(c => c.field === field);
    }

    setColumnPinned(field, pinned) {
        const col = this.getColumn(field);
        if (!col) return false;
        col.pinned = this.sanitizePinned(pinned);
        this.sortColumnsByPinned();
        this.render();
        return true;
    }

    setColumnWidth(field, width) {
        const col = this.getColumn(field);
        if (!col) return false;
        const w = Math.max(20, Math.floor(Number(width) || 0));
        col.width = w;
        this.render();
        return true;
    }

    moveColumnByField(field, toIndex) {
        const fromIndex = this.getColumnIndex(field);
        if (fromIndex < 0) return false;
        // Prevent moving into pinned area implicitly; keep within same pinned group
        const source = this.processedColumns[fromIndex];
        // Build list of indices for same pinned group
        const group = this.processedColumns
            .map((c, i) => ({ c, i }))
            .filter(x => (x.c.pinned || null) === (source.pinned || null))
            .map(x => x.i);
        const min = Math.min(...group);
        const max = Math.max(...group);
        const clamped = Math.max(min, Math.min(max, toIndex));
        this.moveColumn(fromIndex, clamped);
        return true;
    }

    setColumnVisible(field, visible) {
        const col = this.getColumn(field);
        if (!col) return false;
        col.hide = visible === false;
        this.render();
        return true;
    }

    applyColumnState(params = {}) {
        const { state = [], defaultState = {} } = params;
        const byId = new Map(this.processedColumns.map(c => [c.field, c]));
        state.forEach(s => {
            const col = byId.get(s.colId || s.field || s.colKey);
            if (!col) return;
            if (s.pinned !== undefined) col.pinned = this.sanitizePinned(s.pinned);
            if (s.width != null) col.width = Math.max(20, Number(s.width) || col.width || 100);
            if (s.hide !== undefined) col.hide = !!s.hide;
        });
        // Apply defaults for any missing
        this.processedColumns.forEach(col => {
            if (!(state || []).some(s => (s.colId || s.field || s.colKey) === col.field)) {
                if (defaultState.pinned !== undefined) col.pinned = this.sanitizePinned(defaultState.pinned);
                if (defaultState.width != null) col.width = Math.max(20, Number(defaultState.width) || col.width || 100);
                if (defaultState.hide !== undefined) col.hide = !!defaultState.hide;
            }
        });
        this.sortColumnsByPinned();
        this.render();
        return true;
    }

    getColumnState() {
        return this.processedColumns.map(c => ({
            colId: c.field,
            width: c.width,
            pinned: c.pinned || null,
            hide: !!c.hide
        }));
    }

    /**
     * Grid API methods
     */
    setRowData(data) {
        this.data = Array.isArray(data) ? [...data] : [];
        this.processData();
        this.render();
    }

    refreshCells(params = {}) {
        // Minimal: re-render rows (fast with virtualization)
        this.renderRows();
        return true;
    }

    redrawRows(params = {}) {
        // Minimal: re-render rows
        this.renderRows();
        return true;
    }

    ensureColumnVisible(field) {
        const index = this.getColumnIndex(field);
        if (index < 0 || !this.bodyElement) return false;
        const col = this.processedColumns[index];
        if (col.hide) return false;
        if (col.pinned) return true; // already visible

        // Compute cumulative x of the target column (excluding hidden columns)
        let x = 0;
        let leftPinnedWidth = 0;
        let rightPinnedWidth = 0;
        for (let i = 0; i < this.processedColumns.length; i++) {
            const c = this.processedColumns[i];
            const w = c.hide ? 0 : (c.width || 0);
            if (c.pinned === 'left') leftPinnedWidth += w;
            if (c.pinned === 'right') rightPinnedWidth += w;
        }
        for (let i = 0; i < this.processedColumns.length; i++) {
            const c = this.processedColumns[i];
            if (c.pinned) continue; // center scroll only accounts for non-pinned
            if (i === index) break;
            x += (c.hide ? 0 : (c.width || 0));
        }

        const viewportW = this.bodyElement.clientWidth;
        const current = this.hScrollElement ? (this.hScrollElement.scrollLeft || 0) : (this.bodyElement.scrollLeft || 0);
        const colWidth = col.width || 0;

        const viewLeft = current + leftPinnedWidth;
        const viewRight = current + viewportW - rightPinnedWidth;
        const colLeft = leftPinnedWidth + x;
        const colRight = colLeft + colWidth;

        if (colLeft < viewLeft) {
            // scroll so left edge aligns
            const newLeft = colLeft - leftPinnedWidth;
            if (this.hScrollElement) this.hScrollElement.scrollLeft = Math.max(0, newLeft);
            if (this.bodyElement) this.bodyElement.scrollLeft = Math.max(0, newLeft);
        } else if (colRight > viewRight) {
            // scroll so right edge is visible
            const newLeft = (colRight - viewportW) + rightPinnedWidth;
            if (this.hScrollElement) this.hScrollElement.scrollLeft = Math.max(0, newLeft);
            if (this.bodyElement) this.bodyElement.scrollLeft = Math.max(0, newLeft);
        }
        return true;
    }

    ensureIndexVisible(index) {
        if (!this.bodyElement) return false;
        const rowHeight = this.options.rowHeight;
        const viewportH = this.bodyElement.clientHeight;
        const currentTop = this.bodyElement.scrollTop || 0;
        const rowTop = index * rowHeight;
        const rowBottom = rowTop + rowHeight;
        if (rowTop < currentTop) {
            this.bodyElement.scrollTop = rowTop;
        } else if (rowBottom > currentTop + viewportH) {
            this.bodyElement.scrollTop = rowBottom - viewportH;
        }
        return true;
    }

    sizeColumnsToFit() {
        if (!this.bodyElement) return false;
        const viewportW = this.bodyElement.clientWidth;
        // Calculate total pinned widths
        let leftPinned = 0, rightPinned = 0;
        const centerCols = [];
        this.processedColumns.forEach(c => {
            const w = c.hide ? 0 : (c.width || 0);
            if (c.pinned === 'left') leftPinned += w;
            else if (c.pinned === 'right') rightPinned += w;
            else centerCols.push(c);
        });
        const available = Math.max(0, viewportW - leftPinned - rightPinned - this.getScrollbarWidth());
        const current = centerCols.reduce((s, c) => s + (c.hide ? 0 : (c.width || 0)), 0);
        if (available <= 0 || current <= 0 || !centerCols.length) return false;
        const ratio = available / current;
        centerCols.forEach(c => { if (!c.hide) c.width = Math.max(20, Math.floor((c.width || 0) * ratio)); });
        this.render();
        return true;
    }

    /**
     * Measure vertical scrollbar width in body viewport
     */
    getScrollbarWidth() {
        if (!this.bodyElement) return 0;
        return Math.max(0, this.bodyElement.offsetWidth - this.bodyElement.clientWidth);
    }

    /**
     * Render cell content with custom renderers
     */
    renderCellContent(value, column, rowData, rowIndex) {
        // Custom cell renderer
        if (column.cellRenderer && typeof column.cellRenderer === 'function') {
            return column.cellRenderer(value, column, rowData, rowIndex);
        }

        // Type-based rendering
        switch (column.type) {
            case 'number':
                return this.formatNumber(value);
            case 'currency':
                return this.formatCurrency(value);
            case 'date':
                return this.formatDate(value);
            case 'boolean':
                return this.formatBoolean(value);
            case 'status':
                return this.renderStatusCell(value);
            default:
                return this.escapeHtml(String(value || ''));
        }
    }

    /**
     * Render status with colored dot + same-colored text
     */
    renderStatusCell(value) {
        const { className, label } = this.getStatusStyle(value);
        const safe = this.escapeHtml(String(label || ''));
        return `<span class="snap-grid-status ${className}"><span class="dot"></span><span class="text">${safe}</span></span>`;
    }

    /**
     * Map raw status value to a color class
     */
    getStatusStyle(value) {
        const raw = String(value || '').trim();
        const v = raw.toLowerCase();
        // Group statuses to match old grid palette
        const green = ['active', 'live'];
        const amber = ['timed out', 'timeout', 'timed-out'];
        const blue = ['pending', 'under review', 'processing', 'translating', 'auto-uploaded', 'auto uploaded'];
        const red = ['rejected', 'declined', 'removed', 'locked'];
        const gray = ['draft'];

        let className = 'status-gray';
        if (green.includes(v)) className = 'status-green';
        else if (amber.includes(v)) className = 'status-amber';
        else if (blue.includes(v)) className = 'status-blue';
        else if (red.includes(v)) className = 'status-red';
        else if (gray.includes(v)) className = 'status-gray';

        return { className: className, label: raw };
    }

    /**
     * Format number values
     */
    formatNumber(value) {
        if (value == null || value === '') return '';
        const num = Number(value);
        return isNaN(num) ? String(value) : num.toLocaleString();
    }

    /**
     * Format currency values
     */
    formatCurrency(value) {
        if (value == null || value === '') return '';
        const num = Number(value);
        return isNaN(num) ? String(value) : new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(num);
    }

    /**
     * Format date values
     */
    formatDate(value) {
        if (!value) return '';
        const date = new Date(value);
        return isNaN(date.getTime()) ? String(value) : date.toLocaleDateString();
    }

    /**
     * Format boolean values
     */
    formatBoolean(value) {
        if (value == null) return '';
        return value ? '✓' : '✗';
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Handle checkbox cell clicks
     */
    handleCheckboxClick(event) {
        const checkboxCell = event.target.closest('.snap-grid-checkbox-cell');
        if (!checkboxCell) return;

        const rowIndex = parseInt(checkboxCell.dataset.rowIndex);
        this.toggleRowSelection(rowIndex);
        event.stopPropagation();
    }

    /**
     * Handle header checkbox clicks
     */
    handleHeaderCheckboxClick(event) {
        this.toggleAllRowsSelection();
        event.stopPropagation();
    }

    /**
     * Toggle row selection
     */
    toggleRowSelection(rowIndex) {
        if (this.selectedRows.has(rowIndex)) {
            this.selectedRows.delete(rowIndex);
        } else {
            this.selectedRows.add(rowIndex);
        }

        this.updateSelectionState();
        this.render();

        if (this.options.onSelectionChanged) {
            this.options.onSelectionChanged(this.getSelectedData());
        }
    }

    /**
     * Toggle all rows selection
     */
    toggleAllRowsSelection() {
        if (this.allRowsSelected) {
            this.selectedRows.clear();
        } else {
            this.selectedRows.clear();
            for (let i = 0; i < this.sortedData.length; i++) {
                this.selectedRows.add(i);
            }
        }

        this.updateSelectionState();
        this.render();

        if (this.options.onSelectionChanged) {
            this.options.onSelectionChanged(this.getSelectedData());
        }
    }

    /**
     * Update selection state
     */
    updateSelectionState() {
        const totalRows = this.sortedData.length;
        const selectedCount = this.selectedRows.size;

        this.allRowsSelected = selectedCount === totalRows && totalRows > 0;
        this.indeterminateSelection = selectedCount > 0 && selectedCount < totalRows;
    }

    /**
     * Handle header cell clicks for sorting and menu
     */
    handleHeaderClick(event) {
        const headerCell = event.target.closest('.snap-grid-header-cell');
        if (!headerCell) return;

        const field = headerCell.dataset.field;
        const column = this.processedColumns.find(col => col.field === field);

        if (!column) return;

        // Skip non-interactive columns
        if (['checkbox','preview','actions'].includes(column.field)) return;

        // Check if menu button was clicked
        if (event.target.closest('.snap-grid-column-menu-btn')) {
            this.showColumnMenu(headerCell, column);
            return;
        }

        // Handle sorting on header text click
        if (column.sortable === false) return;

        // Toggle sort direction
        const currentSort = this.sortState[field];
        if (currentSort === 'asc') {
            this.sortState[field] = 'desc';
        } else if (currentSort === 'desc') {
            delete this.sortState[field];
        } else {
            this.sortState[field] = 'asc';
        }

        // Clear other sorts (single column sort for now)
        Object.keys(this.sortState).forEach(key => {
            if (key !== field) {
                delete this.sortState[key];
            }
        });

        this.processData();
        this.render();

        // Callback
        if (this.options.onSort) {
            this.options.onSort(field, this.sortState[field]);
        }
    }

    /**
     * Handle row clicks for selection
     */
    handleRowClick(event) {
        const row = event.target.closest('.snap-grid-row');
        if (!row) return;

        const rowIndex = parseInt(row.dataset.index);

        if (this.options.selectable) {
            // Toggle selection via the central method so checkbox UI updates
            this.toggleRowSelection(rowIndex);
        }

        // Callback
        if (this.options.onRowClick) {
            const rowData = this.sortedData[rowIndex];
            this.options.onRowClick(rowData, rowIndex, event);
        }
    }

    /**
     * Handle cell clicks for editing
     */
    handleCellClick(event) {
        const cell = event.target.closest('.snap-grid-cell');
        if (!cell || !cell.classList.contains('editable')) return;

        const field = cell.dataset.field;
        const rowIndex = parseInt(cell.dataset.row);
        const column = this.options.columns.find(col => col.field === field);

        if (!column || column.editable === false) return;

        this.startCellEdit(cell, field, rowIndex);
    }

    /**
     * Start editing a cell
     */
    startCellEdit(cell, field, rowIndex) {
        const currentValue = this.sortedData[rowIndex][field];

        // Create input element
        const input = document.createElement('input');
        input.type = 'text';
        input.value = currentValue || '';
        input.className = 'snap-grid-cell-editor';

        // Replace cell content
        cell.innerHTML = '';
        cell.appendChild(input);
        cell.classList.add('editing');

        // Focus and select
        input.focus();
        input.select();

        // Save on blur or enter
        const saveEdit = () => {
            const newValue = input.value;
            this.finishCellEdit(cell, field, rowIndex, newValue);
        };

        // Cancel on escape
        const cancelEdit = () => {
            this.cancelCellEdit(cell, field, rowIndex);
        };

        input.addEventListener('blur', saveEdit);
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                saveEdit();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });
    }

    /**
     * Finish cell editing
     */
    finishCellEdit(cell, field, rowIndex, newValue) {
        const oldValue = this.sortedData[rowIndex][field];

        // Update data
        this.sortedData[rowIndex][field] = newValue;

        // Update original data array
        const originalIndex = this.data.findIndex(row => row === this.sortedData[rowIndex]);
        if (originalIndex !== -1) {
            this.data[originalIndex][field] = newValue;
        }

        // Re-render cell
        const column = this.options.columns.find(col => col.field === field);
        const cellContent = this.renderCellContent(newValue, column, this.sortedData[rowIndex], rowIndex);

        cell.innerHTML = '';
        if (typeof cellContent === 'string') {
            cell.innerHTML = cellContent;
        } else {
            cell.appendChild(cellContent);
        }

        cell.classList.remove('editing');

        // Callback
        if (this.options.onCellEdit) {
            this.options.onCellEdit(field, newValue, oldValue, this.sortedData[rowIndex], rowIndex);
        }
    }

    /**
     * Cancel cell editing
     */
    cancelCellEdit(cell, field, rowIndex) {
        const column = this.options.columns.find(col => col.field === field);
        const currentValue = this.sortedData[rowIndex][field];
        const cellContent = this.renderCellContent(currentValue, column, this.sortedData[rowIndex], rowIndex);

        cell.innerHTML = '';
        if (typeof cellContent === 'string') {
            cell.innerHTML = cellContent;
        } else {
            cell.appendChild(cellContent);
        }

        cell.classList.remove('editing');
    }

    /**
     * Handle arrow key navigation
     */
    handleArrowKeys(event) {
        const focusedCell = document.activeElement.closest('.snap-grid-cell');
        if (!focusedCell) return;

        const row = focusedCell.closest('.snap-grid-row');
        const rowIndex = parseInt(row.dataset.index);
        const columnIndex = parseInt(focusedCell.dataset.column);

        let newRowIndex = rowIndex;
        let newColumnIndex = columnIndex;

        switch (event.key) {
            case 'ArrowUp':
                newRowIndex = Math.max(0, rowIndex - 1);
                break;
            case 'ArrowDown':
                newRowIndex = Math.min(this.sortedData.length - 1, rowIndex + 1);
                break;
            case 'ArrowLeft':
                newColumnIndex = Math.max(0, columnIndex - 1);
                break;
            case 'ArrowRight':
                newColumnIndex = Math.min(this.options.columns.length - 1, columnIndex + 1);
                break;
        }

        // Find and focus the new cell
        const newCell = document.querySelector(
            `.snap-grid-cell[data-row="${newRowIndex}"][data-column="${newColumnIndex}"]`
        );

        if (newCell) {
            newCell.focus();
            newCell.scrollIntoView({ block: 'nearest', inline: 'nearest' });
        }

        event.preventDefault();
    }

    /**
     * Handle enter key
     */
    handleEnterKey(event) {
        const focusedCell = document.activeElement.closest('.snap-grid-cell');
        if (focusedCell && focusedCell.classList.contains('editable')) {
            const field = focusedCell.dataset.field;
            const rowIndex = parseInt(focusedCell.dataset.row);
            this.startCellEdit(focusedCell, field, rowIndex);
            event.preventDefault();
        }
    }

    /**
     * Handle escape key
     */
    handleEscapeKey(event) {
        const editingCell = document.querySelector('.snap-grid-cell.editing');
        if (editingCell) {
            const field = editingCell.dataset.field;
            const rowIndex = parseInt(editingCell.dataset.row);
            this.cancelCellEdit(editingCell, field, rowIndex);
            event.preventDefault();
        }
    }

    /**
     * Set up column resizing
     */
    setupColumnResize() {
        let isResizing = false;
        let currentColumn = null;
        let startX = 0;
        let startWidth = 0;
        let currentField = null;
        this._resizeRaf = null;

        this.container.addEventListener('mousedown', (e) => {
            const resizeHandle = e.target.closest('.snap-grid-resize-handle');
            if (!resizeHandle) return;

            isResizing = true;
            currentColumn = resizeHandle.closest('.snap-grid-header-cell');
            currentField = currentColumn?.dataset?.field || null;
            startX = e.clientX;
            startWidth = currentColumn.offsetWidth;

            document.body.style.cursor = 'col-resize';
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isResizing || !currentColumn) return;

            const diff = e.clientX - startX;
            const newWidth = Math.max(50, startWidth + diff);

            // Update column width on the live DOM and state
            const field = currentField;
            const column = this.getColumn(field);
            if (column) {
                column.width = newWidth;
                this.updateColumnWidthDOM(field, newWidth);
                // Throttle expensive metrics updates to animation frames
                if (!this._resizeRaf) {
                    this._resizeRaf = requestAnimationFrame(() => {
                        this._resizeRaf = null;
                        this.updateHorizontalMetrics();
                        this.syncHorizontalScroll();
                    });
                }
            }
        });

        document.addEventListener('mouseup', () => {
            if (isResizing) {
                isResizing = false;
                const savedLeft = this.scrollLeft || 0;
                currentColumn = null;
                currentField = null;
                document.body.style.cursor = '';
                // Re-render to recompute pinned offsets and ensure full consistency
                this.render();
                // Restore horizontal scroll position to body & bottom bar
                if (this.hScrollElement && this.hScrollElement.scrollLeft !== savedLeft) {
                    this.hScrollElement.scrollLeft = savedLeft;
                }
                if (this.bodyElement && this.bodyElement.scrollLeft !== savedLeft) {
                    this.bodyElement.scrollLeft = savedLeft;
                }
                this.syncHorizontalScroll();
            }
        });
    }

    /**
     * Update header and visible body cells for a given column field to a new width.
     * Avoids full re-render during drag for smooth UX.
     */
    updateColumnWidthDOM(field, width) {
        const w = Math.max(20, Math.floor(Number(width) || 0));
        // Header cell
        const headerCell = this.headerElement?.querySelector(`.snap-grid-header-cell[data-field="${field}"]`);
        if (headerCell) {
            headerCell.style.width = `${w}px`;
            headerCell.style.minWidth = `${w}px`;
        }
        // Body cells (only visible rows)
        if (this.viewportElement) {
            const cells = this.viewportElement.querySelectorAll(`.snap-grid-cell[data-field="${field}"]`);
            cells.forEach(cell => {
                cell.style.width = `${w}px`;
                cell.style.minWidth = `${w}px`;
            });
        }
        // No extra pinned offset recalculation needed; full render handles consistency
    }

    /**
     * Show column menu
     */
    showColumnMenu(headerCell, column) {
        // Remove existing menus
        document.querySelectorAll('.snap-grid-column-menu').forEach(menu => menu.remove());

        const menu = document.createElement('div');
        menu.className = 'snap-grid-column-menu';

        // Sort options
        if (this.options.sortable && column.sortable !== false) {
            const sortAsc = document.createElement('div');
            sortAsc.className = 'snap-grid-menu-item';
            sortAsc.innerHTML = '<span class="snap-grid-menu-item-icon"><img src="assets/ascending-ic.svg" alt="Ascending" class="menu-icon-img"></span>Sort Ascending';
            sortAsc.addEventListener('click', () => {
                this.setSort(column.field, 'asc');
                menu.remove();
            });
            menu.appendChild(sortAsc);

            const sortDesc = document.createElement('div');
            sortDesc.className = 'snap-grid-menu-item';
            sortDesc.innerHTML = '<span class="snap-grid-menu-item-icon"><img src="assets/descending-ic.svg" alt="Descending" class="menu-icon-img"></span>Sort Descending';
            sortDesc.addEventListener('click', () => {
                this.setSort(column.field, 'desc');
                menu.remove();
            });
            menu.appendChild(sortDesc);

            const clearSort = document.createElement('div');
            clearSort.className = 'snap-grid-menu-item';
            clearSort.innerHTML = '<span class="snap-grid-menu-item-icon">✕</span>Clear Sort';
            clearSort.addEventListener('click', () => {
                this.setSort(column.field, null);
                menu.remove();
            });
            menu.appendChild(clearSort);

            const divider = document.createElement('div');
            divider.className = 'snap-grid-menu-divider';
            menu.appendChild(divider);
        }

        // Filter options
        if (this.options.filterable && column.filterable !== false) {
            const filterContainer = document.createElement('div');
            filterContainer.style.padding = '8px 16px';

            const filterInput = document.createElement('input');
            filterInput.type = 'text';
            filterInput.className = 'snap-grid-filter-input';
            filterInput.placeholder = `Filter ${column.headerName || column.field}...`;
            filterInput.value = this.filterState[column.field]?.value || '';

            filterInput.addEventListener('keyup', (e) => {
                if (e.key === 'Enter') {
                    this.setFilter(column.field, filterInput.value);
                    menu.remove();
                }
            });

            filterContainer.appendChild(filterInput);
            menu.appendChild(filterContainer);

            const clearFilter = document.createElement('div');
            clearFilter.className = 'snap-grid-menu-item';
            clearFilter.innerHTML = '<span class="snap-grid-menu-item-icon">✕</span>Clear Filter';
            clearFilter.addEventListener('click', () => {
                this.setFilter(column.field, '');
                menu.remove();
            });
            menu.appendChild(clearFilter);
        }

        // Position and show menu
        headerCell.style.position = 'relative';
        headerCell.appendChild(menu);

        // Close menu when clicking outside
        setTimeout(() => {
            document.addEventListener('click', function closeMenu(e) {
                if (!menu.contains(e.target)) {
                    menu.remove();
                    document.removeEventListener('click', closeMenu);
                }
            });
        }, 0);
    }

    /**
     * Create ARIA live region for screen reader announcements
     */
    createAriaLiveRegion() {
        this.ariaLiveRegion = document.createElement('div');
        this.ariaLiveRegion.className = 'snap-grid-sr-only';
        this.ariaLiveRegion.setAttribute('aria-live', 'polite');
        this.ariaLiveRegion.setAttribute('aria-atomic', 'true');
        this.container.appendChild(this.ariaLiveRegion);
    }

    /**
     * Announce message to screen readers
     */
    announceToScreenReader(message) {
        if (this.ariaLiveRegion) {
            this.ariaLiveRegion.textContent = message;

            // Clear after announcement
            setTimeout(() => {
                this.ariaLiveRegion.textContent = '';
            }, 1000);
        }
    }

    /**
     * Add accessibility attributes to header cells
     */
    addHeaderAccessibility(headerCell, column, columnIndex) {
        headerCell.setAttribute('role', 'columnheader');
        headerCell.setAttribute('tabindex', '0');
        headerCell.setAttribute('aria-sort', 'none');

        if (this.sortState[column.field]) {
            const direction = this.sortState[column.field] === 'asc' ? 'ascending' : 'descending';
            headerCell.setAttribute('aria-sort', direction);
        }

        // Add column index for screen readers
        headerCell.setAttribute('aria-colindex', columnIndex + 1);

        // Add keyboard instructions
        if (this.options.sortable && column.sortable !== false) {
            headerCell.setAttribute('aria-label',
                `${column.headerName || column.field}, column ${columnIndex + 1}. Click to sort.`);
        }
    }

    /**
     * Add accessibility attributes to data cells
     */
    addCellAccessibility(cell, column, rowData, rowIndex, columnIndex) {
        cell.setAttribute('role', 'gridcell');
        cell.setAttribute('tabindex', '-1');
        cell.setAttribute('aria-colindex', columnIndex + 1);
        cell.setAttribute('aria-rowindex', rowIndex + 2); // +2 because header is row 1

        // Add cell description for screen readers
        const value = rowData[column.field];
        const description = `${column.headerName || column.field}: ${this.formatValueForScreenReader(value, column)}`;
        cell.setAttribute('aria-label', description);

        // Mark editable cells
        if (this.options.editable && column.editable !== false) {
            cell.setAttribute('aria-describedby', 'grid-edit-instructions');
        }
    }

    /**
     * Format value for screen reader announcement
     */
    formatValueForScreenReader(value, column) {
        if (value == null || value === '') return 'empty';

        switch (column.type) {
            case 'currency':
                return `${value} dollars`;
            case 'date':
                const date = new Date(value);
                return isNaN(date.getTime()) ? String(value) : date.toLocaleDateString();
            case 'boolean':
                return value ? 'yes' : 'no';
            default:
                return String(value);
        }
    }

    // ========================================================================
    // PUBLIC API METHODS
    // ========================================================================

    /**
     * Update grid data
     */
    updateData(newData) {
        this.data = [...newData];
        this.processData();
        this.render();

        // Announce data update to screen readers
        this.announceToScreenReader(`Grid updated with ${newData.length} rows`);
    }

    /**
     * Get all data
     */
    getData() {
        return [...this.data];
    }

    /**
     * Get filtered and sorted data
     */
    getDisplayedData() {
        return [...this.sortedData];
    }

    /**
     * Get selected row data
     */
    getSelectedData() {
        return Array.from(this.selectedRows).map(index => this.sortedData[index]);
    }

    /**
     * Clear all selections
     */
    clearSelection() {
        this.selectedRows.clear();
        document.querySelectorAll('.snap-grid-row.selected').forEach(row => {
            row.classList.remove('selected');
        });
    }

    /**
     * Set column definitions
     */
    setColumns(columns) {
        this.options.columns = columns;
        this.render();
    }

    /**
     * Get column definitions
     */
    getColumns() {
        return [...this.options.columns];
    }

    /**
     * Set filter for a column
     */
    setFilter(field, filterValue, filterType = 'contains') {
        if (filterValue) {
            this.filterState[field] = {
                value: filterValue,
                type: filterType
            };
        } else {
            delete this.filterState[field];
        }

        this.processData();
        this.render();

        if (this.options.onFilter) {
            this.options.onFilter(field, filterValue, filterType);
        }
    }

    /**
     * Clear all filters
     */
    clearFilters() {
        this.filterState = {};
        this.processData();
        this.render();
    }

    /**
     * Set sort for a column
     */
    setSort(field, direction) {
        if (direction && ['asc', 'desc'].includes(direction)) {
            this.sortState[field] = direction;
        } else {
            delete this.sortState[field];
        }

        this.processData();
        this.render();

        if (this.options.onSort) {
            this.options.onSort(field, direction);
        }
    }

    /**
     * Clear all sorting
     */
    clearSort() {
        this.sortState = {};
        this.processData();
        this.render();
    }

    /**
     * Resize the grid (call after container size changes)
     */
    resize() {
        this.calculateVisibleRange();
        this.render();
    }

    /**
     * Refresh the grid
     */
    refresh() {
        this.processData();
        this.render();
    }

    /**
     * Destroy the grid and clean up
     */
    destroy() {
        // Remove event listeners
        window.removeEventListener('resize', this.handleResize.bind(this));

        // Clear container
        if (this.container) {
            this.container.innerHTML = '';
            this.container.className = '';
        }

        // Clear references
        this.container = null;
        this.gridElement = null;
        this.headerElement = null;
        this.bodyElement = null;
        this.viewportElement = null;
        this.scrollContainer = null;

        console.log('🗑️ SnapGrid destroyed');
    }

    /**
     * Export data to CSV
     */
    exportToCsv(filename = 'grid-data.csv') {
        const headers = this.options.columns.map(col => col.headerName || col.field);
        const rows = this.sortedData.map(row =>
            this.options.columns.map(col => row[col.field] || '')
        );

        const csvContent = [headers, ...rows]
            .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
            .join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
        URL.revokeObjectURL(link.href);
    }

    /**
     * Get grid statistics
     */
    getStats() {
        return {
            totalRows: this.data.length,
            filteredRows: this.filteredData.length,
            displayedRows: this.sortedData.length,
            selectedRows: this.selectedRows.size,
            lastRenderDuration: this.lastRenderDuration,
            visibleRange: {
                start: this.visibleStartIndex,
                end: this.visibleEndIndex
            }
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SnapGrid;
} else if (typeof window !== 'undefined') {
    window.SnapGrid = SnapGrid;
}
